yarn.nodemanager.linux-container-executor.group=#configured value of yarn.nodemanager.linux-container-executor.group
banned.users=#comma separated list of users who can not run applications
min.user.id=1000#Prevent other super-users
allowed.system.users=##comma separated list of system users who CAN run applications
feature.tc.enabled=false

# The configs below deal with settings for Docker
#[docker]
#  module.enabled=## enable/disable the module. set to "true" to enable, disabled by default
#  docker.binary=/usr/bin/docker
#  docker.allowed.capabilities=## comma seperated capabilities that can be granted, e.g CHOWN,DAC_OVERRIDE,FSETID,FOWNER,MKNOD,NET_RAW,SETGID,SETUID,SETFCAP,SETPCAP,NET_BIND_SERVICE,SYS_CHROOT,KILL,AUDIT_WRITE
#  docker.allowed.devices=## comma seperated list of devices that can be mounted into a container
#  docker.allowed.networks=## comma seperated networks that can be used. e.g bridge,host,none
#  docker.allowed.ro-mounts=## comma seperated volumes that can be mounted as read-only
#  docker.allowed.rw-mounts=## comma seperate volumes that can be mounted as read-write, add the yarn local and log dirs to this list to run Hadoop jobs
#  docker.privileged-containers.enabled=false
#  docker.allowed.volume-drivers=## comma seperated list of allowed volume-drivers
#  docker.no-new-privileges.enabled=## enable/disable the no-new-privileges flag for docker run. Set to "true" to enable, disabled by default
#  docker.allowed.runtimes=## comma seperated runtimes that can be used.

# The configs below deal with settings for FPGA resource
#[fpga]
#  module.enabled=## Enable/Disable the FPGA resource handler module. set to "true" to enable, disabled by default
#  fpga.major-device-number=## Major device number of FPGA, by default is 246. Strongly recommend setting this
#  fpga.allowed-device-minor-numbers=## Comma separated allowed minor device numbers, empty means all FPGA devices managed by YARN.