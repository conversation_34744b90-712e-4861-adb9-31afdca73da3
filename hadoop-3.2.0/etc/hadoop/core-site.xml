<configuration>
  <property>
    <name>fs.defaultFS</name>
    <value>hdfs://*************:9000</value>
  </property>

  <property>
    <name>hadoop.security.authentication</name>
    <value>kerberos</value>
  </property>
<property>
  <name>hadoop.security.authorization</name>
  <value>true</value>
</property>
  <property>
    <name>hadoop.http.authentication.type</name>
    <value>kerberos</value>
  </property>
  <property>

    <name>hadoop.http.authentication.kerberos.principal</name>
    <value>HTTP/<EMAIL></value>
  </property>

  <property>
    <name>hadoop.http.authentication.kerberos.keytab</name>
    <value>/etc/security/keytabs/spnego.service.keytab</value>
  </property>
</configuration>
