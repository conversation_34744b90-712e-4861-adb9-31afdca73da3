<configuration>
  <property>
    <name>dfs.replication</name>
    <value>1</value>
  </property>

  <property>
    <name>dfs.namenode.kerberos.principal</name>
    <value>hdfs/<EMAIL></value>
  </property>

  <property>
    <name>dfs.namenode.keytab.file</name>
    <value>/etc/security/keytabs/hdfs.keytab</value>
  </property>

  <property>
    <name>dfs.datanode.kerberos.principal</name>
    <value>hdfs/<EMAIL></value>
  </property>

  <property>
    <name>dfs.datanode.keytab.file</name>
    <value>/etc/security/keytabs/hdfs.keytab</value>
  </property>

  <property>
    <name>dfs.block.access.token.enable</name>
    <value>true</value>
  </property>

<property>
  <name>dfs.web.authentication.kerberos.principal</name>
  <value>HTTP/<EMAIL></value>
</property>

<property>
  <name>dfs.web.authentication.kerberos.keytab</name>
  <value>/etc/security/keytabs/spnego.service.keytab</value>
</property>

<property>
  <name>dfs.data.transfer.protection</name>
  <value>authentication</value>
</property>

<property>
  <name>dfs.http.policy</name>
  <value>HTTP_ONLY</value>
</property>

<property>
  <name>dfs.datanode.address</name>
  <value>0.0.0.0:10010</value> <!-- 端口大于 1024，避免使用特权端口 -->
</property>

<property>
  <name>dfs.datanode.http.address</name>
  <value>0.0.0.0:10011</value>
</property>

<property>
  <name>dfs.datanode.https.address</name>
  <value>0.0.0.0:10012</value>
</property>
<property>
  <name>dfs.secondary.namenode.kerberos.principal</name>
  <value>hdfs/<EMAIL></value>
</property>

<property>
  <name>dfs.secondary.namenode.keytab.file</name>
  <value>/etc/security/keytabs/hdfs.keytab</value>
</property>

</configuration>
