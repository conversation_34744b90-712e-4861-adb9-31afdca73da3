# Licensed to the Apache Software Foundation (ASF) under one
#  or more contributor license agreements.  See the NOTICE file
#  distributed with this work for additional information
#  regarding copyright ownership.  The ASF licenses this file
#  to you under the Apache License, Version 2.0 (the
#  "License"); you may not use this file except in compliance
#  with the License.  You may obtain a copy of the License at
#
#       http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#

# This is the log4j configuration for Slider Application Master

# Log rotation based on size (256MB) with a max of 20 backup files
log4j.rootLogger=INFO, amlog
log4j.threshhold=ALL
log4j.appender.amlog=org.apache.log4j.RollingFileAppender
log4j.appender.amlog.layout=org.apache.log4j.PatternLayout
log4j.appender.amlog.File=${LOG_DIR}/serviceam.log
log4j.appender.amlog.MaxFileSize=256MB
log4j.appender.amlog.MaxBackupIndex=20

# log layout skips stack-trace creation operations by avoiding line numbers and method
log4j.appender.amlog.layout.ConversionPattern=%d{ISO8601} [%t] %-5p %c{2} - %m%n

# debug edition is much more expensive
#log4j.appender.amlog.layout.ConversionPattern=%d{ISO8601} [%t] %-5p %c{2} (%F:%M(%L)) - %m%n

# configure stderr
# set the conversion pattern of stderr
# Print the date in ISO 8601 format
log4j.appender.stderr=org.apache.log4j.ConsoleAppender
log4j.appender.stderr.Target=System.err
log4j.appender.stderr.layout=org.apache.log4j.PatternLayout
log4j.appender.stderr.layout.ConversionPattern=%d{ISO8601} [%t] %-5p %c{2} - %m%n

log4j.appender.subprocess=org.apache.log4j.ConsoleAppender
log4j.appender.subprocess.layout=org.apache.log4j.PatternLayout
log4j.appender.subprocess.layout.ConversionPattern=[%c{1}]: %m%n

# for debugging yarn-service framework
#log4j.logger.org.apache.hadoop.yarn.service=DEBUG

# uncomment for YARN operations
#log4j.logger.org.apache.hadoop.yarn.client=DEBUG

# uncomment this to debug security problems
#log4j.logger.org.apache.hadoop.security=DEBUG

#crank back on some noise
log4j.logger.org.apache.hadoop.util.NativeCodeLoader=ERROR
log4j.logger.org.apache.hadoop.hdfs=WARN

log4j.logger.org.apache.zookeeper=WARN
log4j.logger.org.apache.curator.framework.state=ERROR
log4j.logger.org.apache.curator.framework.imps=WARN
