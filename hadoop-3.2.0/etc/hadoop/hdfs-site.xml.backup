<configuration>

  <!-- 基础配置 -->
  <property>
    <name>dfs.replication</name>
    <value>1</value>
  </property>
<property>
  <name>dfs.namenode.rpc-address</name>
  <value>*************:9000</value>
</property>

  <!-- 启用 block access token -->
  <property>
    <name>dfs.block.access.token.enable</name>
    <value>true</value>
  </property>

  <!-- NameNode 安全配置 -->
  <property>
  <name>dfs.https.address</name>
  <value>*************:50470</value>
</property>
<property>
  <name>dfs.https.port</name>
  <value>50470</value>
  </property>
  <property>
    <name>dfs.namenode.kerberos.principal</name>
    <value>hdfs/<EMAIL></value>
  </property>
  <property>
    <name>dfs.namenode.keytab.file</name>
    <value>/etc/security/keytabs/hdfs.keytab</value>
  </property>
  <!-- SecondaryNameNode 安全配置 -->
  <property>
  <name>dfs.secondary.https.address</name>
  <value>*************:50495</value>
</property>
<property>
  <name>dfs.secondary.https.port</name>
  <value>50495</value>
</property>
  <property>
    <name>dfs.secondary.namenode.kerberos.principal</name>
    <value>hdfs/<EMAIL></value>
  </property>
  <property>
    <name>dfs.secondary.namenode.keytab.file</name>
    <value>/etc/security/keytabs/hdfs.keytab</value>
  </property>

  <!-- Web 认证配置 -->
  <property>
    <name>dfs.web.authentication.kerberos.principal</name>
    <value>HTTP/<EMAIL></value>
  </property>
  <property>
    <name>dfs.web.authentication.kerberos.keytab</name>
    <value>/etc/security/keytabs/spnego.service.keytab</value>
  </property>

  <!-- DataNode 安全配置 -->
  <property>
    <name>dfs.datanode.kerberos.principal</name>
    <value>hdfs/<EMAIL></value>
  </property>
  <property>
    <name>dfs.datanode.keytab.file</name>
    <value>/etc/security/keytabs/hdfs.keytab</value>
  </property>
<property>
  <name>dfs.datanode.kerberos.https.principal</name>
  <value>hdfs/<EMAIL></value>
</property>
<property>
  <name>dfs.data.transfer.protection</name>
  <value>integrity</value>
</property>
<property>
  <name>dfs.http.policy</name>
  <value>HTTP_ONLY</value>
</property>
  <property>
    <name>dfs.datanode.address</name>
    <value>0.0.0.0:10004</value>
  </property>
  <property>
    <name>dfs.datanode.http.address</name>
    <value>0.0.0.0:10006</value>
  </property>
</configuration>
