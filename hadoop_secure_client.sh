#!/bin/bash



# Hadoop安全集群Shell客户端脚本

# 支持Kerberos认证和SSL加密



# 配置参数

NAMENODE_HOST="*************"

NAMENODE_PORT="9000"

NAMENODE_HTTPS_PORT="50470"

HADOOP_HOME="/home/<USER>/hadoop-3.2.0"

HADOOP_CONF_DIR="$HADOOP_HOME/etc/hadoop"



# Kerberos配置

REALM="HADOOP.COM"

PRINCIPAL="hdfs/<EMAIL>"

KEYTAB_FILE="/etc/security/keytabs/hdfs.keytab"

KRB5_CONF="/etc/krb5.conf"



# 颜色输出

RED='\033[0;31m'

GREEN='\033[0;32m'

YELLOW='\033[1;33m'

BLUE='\033[0;34m'

NC='\033[0m' # No Color



# 日志函数

log_info() {

    echo -e "${BLUE}[INFO]${NC} $1"

}



log_success() {

    echo -e "${GREEN}[SUCCESS]${NC} $1"

}



log_warning() {

    echo -e "${YELLOW}[WARNING]${NC} $1"

}



log_error() {

    echo -e "${RED}[ERROR]${NC} $1"

}



# 检查依赖

check_dependencies() {

    log_info "检查依赖..."

    

    # 检查Hadoop是否安装

    if [ ! -d "$HADOOP_HOME" ]; then

        log_error "Hadoop未找到: $HADOOP_HOME"

        exit 1

    fi

    

    # 检查配置文件

    if [ ! -f "$HADOOP_CONF_DIR/core-site.xml" ]; then

        log_error "Hadoop配置文件未找到: $HADOOP_CONF_DIR/core-site.xml"

        exit 1

    fi

    

    # 检查Kerberos工具

    if ! command -v kinit &> /dev/null; then

        log_error "kinit命令未找到，请安装Kerberos客户端"

        exit 1

    fi

    

    # 检查keytab文件

    if [ ! -f "$KEYTAB_FILE" ]; then

        log_error "Keytab文件未找到: $KEYTAB_FILE"

        exit 1

    fi

    

    log_success "依赖检查通过"

}



# 设置环境变量

setup_environment() {

    log_info "设置环境变量..."

    

    export HADOOP_HOME="$HADOOP_HOME"

    export HADOOP_CONF_DIR="$HADOOP_CONF_DIR"

    export PATH="$HADOOP_HOME/bin:$HADOOP_HOME/sbin:$PATH"

    export KRB5_CONFIG="$KRB5_CONF"

    export JAVA_HOME="${JAVA_HOME:-/usr/lib/jvm/java-8-openjdk-amd64}"

    

    # SSL相关环境变量

    export HADOOP_OPTS="$HADOOP_OPTS -Djava.security.krb5.conf=$KRB5_CONF"

    export HADOOP_OPTS="$HADOOP_OPTS -Djavax.net.ssl.trustStore=$HADOOP_CONF_DIR/truststore"

    export HADOOP_OPTS="$HADOOP_OPTS -Djavax.net.ssl.trustStorePassword=123456"

    

    log_success "环境变量设置完成"

}



# Kerberos认证

kerberos_auth() {

    log_info "进行Kerberos认证..."

    

    # 使用keytab进行认证

    kinit -kt "$KEYTAB_FILE" "$PRINCIPAL"

    

    if [ $? -eq 0 ]; then

        log_success "Kerberos认证成功"

        

        # 显示当前票据

        log_info "当前Kerberos票据:"

        klist

        return 0

    else

        log_error "Kerberos认证失败"

        return 1

    fi

}



# 测试HDFS连接

test_hdfs_connection() {

    log_info "测试HDFS连接..."

    

    # 测试基本连接

    if hdfs dfsadmin -report & > /dev/null; then

        log_success "HDFS连接成功"

        return 0

    else

        log_error "HDFS连接失败"

        return 1

    fi

}



# 执行HDFS操作

perform_hdfs_operations() {

    log_info "执行HDFS操作..."

    

    echo

    log_info "1. 显示HDFS集群状态:"

    hdfs dfsadmin -report

    

    echo

    log_info "2. 列出根目录:"

    hdfs dfs -ls /

    

    echo

    log_info "3. 创建测试目录:"

    TEST_DIR="/test-shell-client"

    hdfs dfs -mkdir -p "$TEST_DIR"

    if [ $? -eq 0 ]; then

        log_success "成功创建目录: $TEST_DIR"

    else

        log_warning "目录可能已存在: $TEST_DIR"

    fi

    

    echo

    log_info "4. 创建测试文件:"

    TEST_FILE="$TEST_DIR/test.txt"

    echo "Hello, Secure Hadoop Cluster from Shell!" | hdfs dfs -put - "$TEST_FILE"

    if [ $? -eq 0 ]; then

        log_success "成功创建文件: $TEST_FILE"

    else

        log_warning "文件创建失败或已存在: $TEST_FILE"

    fi

    

    echo

    log_info "5. 读取文件内容:"

    hdfs dfs -cat "$TEST_FILE"

    

    echo

    log_info "6. 显示文件信息:"

    hdfs dfs -stat "%n - 大小: %o bytes, 修改时间: %y" "$TEST_FILE"

    

    echo

    log_info "7. 列出测试目录:"

    hdfs dfs -ls "$TEST_DIR"

    

    log_success "HDFS操作完成"

}



# WebHDFS API测试

test_webhdfs_api() {

    log_info "测试WebHDFS API..."

    

    # 使用curl测试WebHDFS

    WEBHDFS_URL="https://$NAMENODE_HOST:$NAMENODE_HTTPS_PORT/webhdfs/v1"

    

    # 获取根目录列表

    log_info "获取根目录列表:"

    curl -k --negotiate -u : "$WEBHDFS_URL/?op=LISTSTATUS" 2>/dev/null | python3 -m json.tool

    

    if [ $? -eq 0 ]; then

        log_success "WebHDFS API测试成功"

    else

        log_warning "WebHDFS API测试失败，可能需要配置SPNEGO认证"

    fi

}



# 清理函数

cleanup() {

    log_info "清理资源..."

    # 可以在这里添加清理逻辑

    log_success "清理完成"

}



# 主函数

main() {

    echo "=== Hadoop安全集群Shell客户端 ==="

    echo

    

    # 设置错误时退出

    set -e

    

    # 设置清理函数

    trap cleanup EXIT

    

    # 执行步骤

    check_dependencies

    setup_environment

    

    if kerberos_auth; then

        if test_hdfs_connection; then

            perform_hdfs_operations

            echo

            test_webhdfs_api

        else

            log_error "HDFS连接测试失败，请检查配置"

            exit 1

        fi

    else

        log_error "Kerberos认证失败，无法继续"

        exit 1

    fi

    

    echo

    log_success "所有操作完成!"

}



# 显示帮助信息

show_help() {

    echo "用法: $0 [选项]"

    echo

    echo "选项:"

    echo "  -h, --help     显示帮助信息"

    echo "  -t, --test     仅测试连接"

    echo "  -c, --config   显示配置信息"

    echo

    echo "示例:"

    echo "  $0              # 运行完整测试"

    echo "  $0 --test       # 仅测试连接"

    echo "  $0 --config     # 显示配置"

}



# 显示配置信息

show_config() {

    echo "=== 当前配置 ==="

    echo "NameNode: $NAMENODE_HOST:$NAMENODE_PORT"

    echo "HTTPS端口: $NAMENODE_HTTPS_PORT"

    echo "Hadoop目录: $HADOOP_HOME"

    echo "配置目录: $HADOOP_CONF_DIR"

    echo "Kerberos域: $REALM"

    echo "主体: $PRINCIPAL"

    echo "Keytab: $KEYTAB_FILE"

    echo "Krb5配置: $KRB5_CONF"

}



# 参数处理

case "${1:-}" in

    -h|--help)

        show_help

        exit 0

        ;;

    -c|--config)

        show_config

        exit 0

        ;;

    -t|--test)

        check_dependencies

        setup_environment

        kerberos_auth

        test_hdfs_connection

        exit 0

        ;;

    "")

        main

        ;;

    *)

        echo "未知选项: $1"

        show_help

        exit 1

        ;;

esac
