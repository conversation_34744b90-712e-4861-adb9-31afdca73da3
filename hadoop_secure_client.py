#!/usr/bin/env python3

# -*- coding: utf-8 -*-



"""

Hadoop安全集群Python客户端

支持Kerberos认证和SSL加密连接

"""



import os

import subprocess

import requests

from requests_kerberos import HTTPKerberosAuth, OPTIONAL

from hdfs import InsecureClient, Client

from hdfs.ext.kerberos import KerberosClient

import ssl

import urllib3



# 禁用SSL警告

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)



class HadoopSecureClient:

    def __init__(self):

        # 服务器配置

        self.namenode_host = "*************"

        self.namenode_port = 9000

        self.namenode_http_port = 50070

        self.namenode_https_port = 50470

        

        # Kerberos配置

        self.realm = "HADOOP.COM"

        self.principal = "hdfs/<EMAIL>"

        self.keytab_file = "/etc/security/keytabs/hdfs.keytab"

        self.krb5_conf = "/etc/krb5.conf"

        

        # SSL配置

        self.truststore_path = "/home/<USER>/hadoop-3.2.0/etc/hadoop/truststore"

        self.truststore_password = "123456"

        

        # 设置环境变量

        self.setup_environment()

        

    def setup_environment(self):

        """设置环境变量"""

        os.environ['KRB5_CONFIG'] = self.krb5_conf

        os.environ['HADOOP_CONF_DIR'] = '/home/<USER>/hadoop-3.2.0/etc/hadoop'

        

    def kinit_with_keytab(self):

        """使用keytab进行Kerberos认证"""

        try:

            cmd = f"kinit -kt {self.keytab_file} {self.principal}"

            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

            

            if result.returncode == 0:

                print("Kerberos认证成功")

                return True

            else:

                print(f"Kerberos认证失败: {result.stderr}")

                return False

        except Exception as e:

            print(f"Kerberos认证异常: {e}")

            return False

    

    def check_kerberos_ticket(self):

        """检查Kerberos票据状态"""

        try:

            result = subprocess.run("klist", shell=True, capture_output=True, text=True)

            if result.returncode == 0:

                print("当前Kerberos票据:")

                print(result.stdout)

                return True

            else:

                print("没有有效的Kerberos票据")

                return False

        except Exception as e:

            print(f"检查Kerberos票据异常: {e}")

            return False

    

    def connect_with_webhdfs(self):

        """使用WebHDFS API连接"""

        try:

            # 使用HTTPS连接

            base_url = f"https://{self.namenode_host}:{self.namenode_https_port}"

            

            # 创建带Kerberos认证的会话

            session = requests.Session()

            session.auth = HTTPKerberosAuth(mutual_authentication=OPTIONAL)

            session.verify = False  # 忽略SSL证书验证（生产环境应该使用正确的证书）

            

            # 测试连接 - 获取文件系统状态

            url = f"{base_url}/webhdfs/v1/?op=GETFILESTATUS"

            response = session.get(url)

            

            if response.status_code == 200:

                print("WebHDFS连接成功!")

                print(f"响应: {response.json()}")

                return session, base_url

            else:

                print(f"WebHDFS连接失败: {response.status_code} - {response.text}")

                return None, None

                

        except Exception as e:

            print(f"WebHDFS连接异常: {e}")

            return None, None

    

    def connect_with_hdfs3_client(self):

        """使用hdfs3客户端连接"""

        try:

            # 注意：hdfs3需要安装libhdfs3

            from hdfs3 import HDFileSystem

            

            # 创建HDFS客户端

            fs = HDFileSystem(

                host=self.namenode_host,

                port=self.namenode_port,

                user='hdfs',

                ticket_cache=None,  # 使用系统的Kerberos票据缓存

                token=None

            )

            

            # 测试连接

            files = fs.ls('/')

            print("hdfs3客户端连接成功!")

            print(f"根目录文件: {files}")

            return fs

            

        except ImportError:

            print("hdfs3库未安装，请使用: pip install hdfs3")

            return None

        except Exception as e:

            print(f"hdfs3客户端连接异常: {e}")

            return None

    

    def perform_hdfs_operations(self, session, base_url):

        """执行HDFS操作"""

        if not session or not base_url:

            print("没有有效的连接")

            return

        

        try:

            print("\n=== 执行HDFS操作 ===")

            

            # 1. 列出根目录

            print("\n1. 列出根目录:")

            url = f"{base_url}/webhdfs/v1/?op=LISTSTATUS"

            response = session.get(url)

            if response.status_code == 200:

                files = response.json()['FileStatuses']['FileStatus']

                for file_info in files:

                    file_type = "目录" if file_info['type'] == 'DIRECTORY' else "文件"

                    print(f"  {file_info['pathSuffix']} - {file_type} - 大小: {file_info['length']} bytes")

            

            # 2. 创建测试目录

            print("\n2. 创建测试目录:")

            test_dir = "/test-python-client"

            url = f"{base_url}/webhdfs/v1{test_dir}?op=MKDIRS"

            response = session.put(url)

            if response.status_code == 200:

                print(f"  成功创建目录: {test_dir}")

            else:

                print(f"  创建目录失败: {response.text}")

            

            # 3. 上传文件

            print("\n3. 上传测试文件:")

            test_file = f"{test_dir}/test.txt"

            test_content = "Hello, Secure Hadoop Cluster!"

            

            # 第一步：创建文件

            url = f"{base_url}/webhdfs/v1{test_file}?op=CREATE&overwrite=true"

            response = session.put(url, allow_redirects=False)

            

            if response.status_code == 307:  # 重定向到DataNode

                redirect_url = response.headers['Location']

                # 第二步：上传数据到DataNode

                upload_response = session.put(redirect_url, data=test_content)

                if upload_response.status_code == 201:

                    print(f"  成功上传文件: {test_file}")

                else:

                    print(f"  上传文件失败: {upload_response.text}")

            

            # 4. 读取文件

            print("\n4. 读取文件内容:")

            url = f"{base_url}/webhdfs/v1{test_file}?op=OPEN"

            response = session.get(url, allow_redirects=True)

            if response.status_code == 200:

                print(f"  文件内容: {response.text}")

            

            print("\nHDFS操作完成!")

            

        except Exception as e:

            print(f"HDFS操作异常: {e}")

    

    def run(self):

        """运行客户端"""

        print("=== Hadoop安全集群客户端 ===")

        

        # 1. 进行Kerberos认证

        print("\n1. 进行Kerberos认证...")

        if not self.kinit_with_keytab():

            print("Kerberos认证失败，退出")

            return

        

        # 2. 检查票据状态

        print("\n2. 检查Kerberos票据...")

        self.check_kerberos_ticket()

        

        # 3. 连接HDFS

        print("\n3. 连接HDFS...")

        session, base_url = self.connect_with_webhdfs()

        

        # 4. 执行HDFS操作

        if session:

            self.perform_hdfs_operations(session, base_url)

        

        # 5. 尝试hdfs3客户端（可选）

        print("\n5. 尝试hdfs3客户端...")

        fs = self.connect_with_hdfs3_client()



if __name__ == "__main__":

    client = HadoopSecureClient()

    client.run() 
